/* Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
body {
  font-family: Arial, sans-serif;
  background: #f4f6f8;
  color: #333;
  line-height: 1.6;
}
.container {
  width: 90%;
  max-width: 1100px;
  margin: auto;
}

/* NAVBAR */
.navbar {
  background: #ffffff;
  padding: 15px 20px;
  box-shadow: 0 2px 6px rgba(0,0,0,0.1);
  text-align: center;
  position: sticky;
  top: 0;
  z-index: 1000;
}
.logo-img {
  max-height: 60px;
  width: auto;
}

/* HERO WITH FORM */
.hero {
  position: relative;
  padding: 40px 20px;
  background: linear-gradient(
    135deg,
    rgba(30, 136, 229, 0.85),
    rgba(66, 165, 245, 0.85)
  );
  color: white;
}
.hero-content {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  justify-content: space-between;
  gap: 30px;
}
.hero-text {
  flex: 1;
  min-width: 260px;
}
.hero-text h1 {
  font-size: 2.2rem;
  margin-bottom: 10px;
}
.hero-text p {
  font-size: 1.05rem;
}

/* FORM STYLING */
.hero-form {
  flex-basis: 320px;
  background: white;
  color: #333;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}
.hero-form h3 {
  margin-bottom: 14px;
  color: #1e88e5;
}
.hero-form input,
.hero-form textarea {
  width: 100%;
  padding: 10px;
  margin-bottom: 12px;
  border: 1px solid #ccc;
  border-radius: 5px;
  font-size: 0.95rem;
  resize: vertical;
}
.hero-form button {
  width: 100%;
  padding: 12px;
  background: #1e88e5;
  color: white;
  border: none;
  border-radius: 6px;
  font-weight: bold;
  cursor: pointer;
  font-size: 1rem;
  transition: background 0.3s;
}
.hero-form button:hover {
  background: #1565c0;
}

/* PROGRAMS SECTION */
.programs {
  background: white;
  padding: 40px 20px;
  text-align: center;
}
.programs h2 {
  margin-bottom: 30px;
  font-size: 2rem;
  color: #1e88e5;
}
.program-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
  gap: 24px;
}
.program-card {
  background: #f8f9fa;
  padding: 22px;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 340px;
}
.program-card img {
  width: 90%;
  max-width: 110px;
  height: 80px;
  object-fit: cover;
  border-radius: 7px;
  margin-bottom: 14px;
}
.program-card h3 {
  color: #1565c0;
  margin-bottom: 9px;
  font-size: 1.1rem;
  font-weight: bold;
}
.program-card ul {
  list-style: none;
  text-align: left;
  font-size: 0.97rem;
  padding-left: 0;
}
.program-card ul li {
  margin-bottom: 7px;
}

/* WHY CHOOSE US SECTION */
.why-choose {
  background: #e3eaf3;
  padding: 40px 20px;
  text-align: center;
}
.why-choose h2 {
  margin-bottom: 30px;
  font-size: 2rem;
  color: #1e88e5;
}
.why-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 28px;
}
.why-card {
  background: #fff;
  border-radius: 10px;
  padding: 22px;
  flex-basis: 260px;
  max-width: 320px;
}
.why-card h3 {
  color: #1565c0;
  margin-bottom: 9px;
  font-size: 1.05rem;
}
.why-card p {
  font-size: 0.97rem;
}

/* FLEXIBLE DELIVERY SECTION */
.flexible-delivery {
  background: #fff;
  padding: 40px 20px;
  text-align: center;
}
.flexible-delivery h2 {
  margin-bottom: 32px;
  font-size: 2rem;
  color: #1e88e5;
}
.delivery-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 32px;
}
.delivery-card {
  background: #f0f7fa;
  border-radius: 10px;
  padding: 22px;
  flex-basis: 270px;
  max-width: 320px;
}
.delivery-card h3 {
  color: #1e88e5;
  margin-bottom: 10px;
}
.delivery-card ul {
  list-style: disc inside;
  text-align: left;
  font-size: 0.95rem;
}
.delivery-card ul li {
  margin-bottom: 7px;
}

/* CONTACT SECTION */
.contact {
  background: #1e88e5;
  color: white;
  padding: 40px 20px;
  text-align: center;
}
.contact h2 {
  font-size: 1.6rem;
  margin-bottom: 8px;
}
.contact p {
  font-size: 1.05rem;
  margin-bottom: 15px;
}
.btn {
  background: white;
  color: #1e88e5;
  padding: 12px 28px;
  border-radius: 6px;
  text-decoration: none;
  font-weight: bold;
  transition: background 0.3s;
  display: inline-block;
}
.btn:hover {
  background: #e3eaf3;
}

/* FOOTER */
footer {
  background: #0d47a1;
  color: white;
  text-align: center;
  padding: 20px 15px;
  border-top-left-radius: 14px;
  border-top-right-radius: 14px;
}
.footer-container {
  max-width: 1100px;
  margin: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}
.footer-container p {
  margin: 6px 0;
  font-size: 0.95rem;
  line-height: 1.4;
}
.footer-nav {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 18px;
  font-weight: 500;
}
.footer-nav a {
  color: #bbdefb;
  text-decoration: none;
  transition: color 0.3s;
}
.footer-nav a:hover {
  color: white;
}

/* Specific footer sections */
.footer-nav.quick-links {
  margin-bottom: 8px;
}
.footer-nav.legal-links {
  margin-top: 12px;
}

/* RESPONSIVE */
@media (max-width: 900px) {
  .why-grid,
  .delivery-grid {
    flex-direction: column;
  }
}
@media (max-width: 768px) {
  .hero-content {
    flex-direction: column;
  }
  .footer-nav {
    flex-direction: column;
    gap: 10px;
  }
}
